{"version": 3, "sources": ["src_pages_personal-center_index_tsx-async.5186543058506587458.hot-update.js", "src/pages/personal-center/TeamListCard.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/pages/personal-center/index.tsx',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='11101171475863017292';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"src/pages/personal-center/index.tsx\"],\"src/pages/settings/index.tsx\":[\"p__settings__index\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"]});;\r\n  },\r\n);\r\n", "import {\n  CarOutlined,\n  CheckCircleOutlined,\n  ClockCircleOutlined,\n  CrownOutlined,\n  ExclamationCircleOutlined,\n  LogoutOutlined,\n  MinusCircleOutlined,\n  RightOutlined,\n  SettingOutlined,\n  TeamOutlined,\n  UserOutlined,\n} from '@ant-design/icons';\nimport { history, useModel } from '@umijs/max';\nimport {\n  Al<PERSON>,\n  Button,\n  Col,\n  Flex,\n  message,\n  Modal,\n  Row,\n  Spin,\n  Tooltip,\n  Typography,\n} from 'antd';\nimport { ProCard, ProList } from '@ant-design/pro-components';\nimport React, { useEffect, useState } from 'react';\nimport { AuthService } from '@/services';\nimport { TeamService } from '@/services/team';\nimport type { TeamDetailResponse } from '@/types/api';\nimport {\n  getTeamIdFromCurrentToken,\n  hasTeamInCurrentToken,\n  getUserIdFromCurrentToken,\n} from '@/utils/tokenUtils';\nimport { recordTeamSelection, hasUserSelectedTeam } from '@/utils/teamSelectionUtils';\nimport TeamManagementModal from './components/TeamManagementModal';\n\nconst { Text, Title } = Typography;\n \n\n\n\n// 响应式布局样式\nconst styles = `\n  .team-item .ant-pro-card-body {\n    padding: 0 !important;\n  }\n\n  .team-item:hover {\n    transform: translateY(-1px);\n    box-shadow: 0 4px 12px rgba(0,0,0,0.1) !important;\n  }\n\n  @media (max-width: 768px) {\n    .team-item {\n      margin-bottom: 8px;\n    }\n\n    .team-stats-row {\n      margin-top: 8px;\n    }\n\n    .team-info-wrap {\n      gap: 8px !important;\n    }\n  }\n\n  @media (max-width: 576px) {\n    .team-stats-row {\n      margin-top: 12px;\n    }\n\n    .team-stats-col {\n      margin-bottom: 4px;\n    }\n\n    .team-info-wrap {\n      gap: 6px !important;\n    }\n\n    .team-meta-info {\n      flex-wrap: wrap;\n      gap: 6px !important;\n    }\n\n    .team-status-badges {\n      flex-wrap: wrap;\n      gap: 4px !important;\n      margin-top: 4px;\n    }\n  }\n\n  @media (max-width: 480px) {\n    .team-name-text {\n      font-size: 14px !important;\n    }\n\n    .team-meta-text {\n      font-size: 11px !important;\n    }\n\n    .team-meta-info {\n      gap: 4px !important;\n    }\n\n    .team-status-badges {\n      gap: 3px !important;\n    }\n  }\n`;\n\n/**\n * 团队列表卡片组件\n *\n * 这是个人中心页面的核心组件，负责显示用户所属的团队列表，\n * 并提供团队切换、创建团队等功能。是团队管理系统的重要入口。\n *\n * 主要功能：\n * 1. 显示用户所属的所有团队\n * 2. 支持团队切换功能\n * 3. 支持创建新团队\n * 4. 显示当前选择的团队状态\n * 5. 处理团队切换过程中的状态管理\n *\n * 状态管理：\n * - 团队列表数据的获取和显示\n * - 团队切换过程的加载状态\n * - 创建团队模态框的状态\n * - 错误状态的处理和显示\n *\n * 团队切换逻辑：\n * 1. 检查用户登录状态\n * 2. 判断是否为当前团队（避免重复切换）\n * 3. 调用后端API进行团队切换\n * 4. 更新本地Token和全局状态\n * 5. 跳转到团队仪表盘\n *\n * 与全局状态的集成：\n * - 监听用户登录状态变化\n * - 同步团队切换后的状态更新\n * - 处理用户注销时的状态清理\n */\nconst TeamListCard: React.FC = () => {\n  /**\n   * 团队列表相关状态管理\n   *\n   * 这些状态用于管理团队列表的显示和交互：\n   * - teams: 用户所属的团队列表数据\n   * - loading: 团队列表加载状态\n   * - error: 错误信息（如网络错误、权限错误等）\n   * - switchingTeamId: 当前正在切换的团队ID（用于显示加载状态）\n   */\n  const [teams, setTeams] = useState<TeamDetailResponse[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [switchingTeamId, setSwitchingTeamId] = useState<number | null>(null);\n\n  // 模态框状态管理\n  const [teamManagementModalVisible, setTeamManagementModalVisible] = useState(false);\n  const [leaveTeamModalVisible, setLeaveTeamModalVisible] = useState(false);\n  const [selectedTeam, setSelectedTeam] = useState<TeamDetailResponse | null>(null);\n\n  /**\n   * 创建团队功能已移至设置页面\n   *\n   * 为了更好的用户体验和功能组织，创建团队功能已经移动到\n   * 专门的设置页面中。用户可以通过\"团队设置\"按钮跳转到\n   * 设置页面进行团队创建和管理操作。\n   */\n\n  /**\n   * 全局状态管理\n   *\n   * 从UmiJS全局状态中获取用户和团队信息：\n   * - initialState: 包含当前用户和团队信息的全局状态\n   * - setInitialState: 更新全局状态的函数\n   * - currentTeam: 当前选择的团队信息\n   */\n  const { initialState, setInitialState } = useModel('@@initialState');\n  const currentTeam = initialState?.currentTeam;\n\n  /**\n   * Token信息提取\n   *\n   * 从当前存储的Token中提取关键信息，用于状态判断和权限检查：\n   * - currentTokenTeamId: Token中包含的团队ID\n   * - currentUserId: Token中包含的用户ID\n   * - hasTeamInToken: Token是否包含团队信息\n   *\n   * 这些信息用于：\n   * - 判断当前是否已选择团队\n   * - 确定哪个团队是当前激活的团队\n   * - 记录用户的团队选择历史\n   */\n  const currentTokenTeamId = getTeamIdFromCurrentToken();\n  const currentUserId = getUserIdFromCurrentToken();\n  const hasTeamInToken = hasTeamInCurrentToken();\n\n  // 判断是否有真正的当前团队：\n  // 1. Token中有团队信息（说明用户已经选择过团队）\n  // 2. initialState中有团队信息（说明已经获取过团队详情）\n  // 3. 两者的团队ID一致（确保状态同步）\n  // 4. 用户曾经主动选择过这个团队（区分初始登录和主动选择）\n  const hasRealCurrentTeam = !!(\n    hasTeamInToken &&\n    currentTokenTeamId &&\n    currentTeam &&\n    currentTeam.id === currentTokenTeamId &&\n    currentUserId &&\n    hasUserSelectedTeam(currentUserId, currentTokenTeamId)\n  );\n\n  // 获取实际的当前团队ID：只有在用户真正选择过团队且状态同步时才返回团队ID\n  const actualCurrentTeamId = hasRealCurrentTeam ? currentTokenTeamId : null;\n\n  // 调试日志\n  console.log('TeamListCard 状态调试:', {\n    currentTeam: currentTeam?.id,\n    currentTokenTeamId,\n    currentUserId,\n    hasTeamInToken,\n    hasRealCurrentTeam,\n    actualCurrentTeamId,\n    hasUserSelectedCurrentTeam: currentUserId && currentTokenTeamId ? hasUserSelectedTeam(currentUserId, currentTokenTeamId) : false,\n    initialStateCurrentUser: !!initialState?.currentUser,\n  });\n\n  // 获取团队列表数据\n  const fetchTeams = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      const teamsData = await TeamService.getUserTeamsWithStats();\n      setTeams(teamsData);\n    } catch (error) {\n      console.error('获取团队列表失败:', error);\n      setError('获取团队列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    // 只有在用户已登录时才获取团队列表\n    if (initialState?.currentUser) {\n      fetchTeams();\n    }\n  }, [initialState?.currentUser]);\n\n  // 监听全局状态变化，处理注销等情况\n  useEffect(() => {\n    // 如果用户已注销（currentUser为undefined），清除本地团队列表状态\n    if (!initialState?.currentUser) {\n      setTeams([]);\n      setError(null);\n      setLoading(false);\n      setSwitchingTeamId(null);\n    }\n  }, [initialState?.currentUser]);\n\n  // 监听当前团队状态变化\n  useEffect(() => {\n    console.log('当前团队状态变化:', {\n      currentTeam: currentTeam?.id,\n      actualCurrentTeamId,\n      hasRealCurrentTeam,\n    });\n  }, [currentTeam?.id, actualCurrentTeamId, hasRealCurrentTeam]);\n\n  // 创建团队功能已移至设置页面，此处不再需要处理函数\n\n  /**\n   * 团队切换处理函数\n   *\n   * 这是团队切换功能的核心函数，处理用户从一个团队切换到另一个团队的完整流程。\n   * 包括权限检查、API调用、状态更新、页面跳转等步骤。\n   *\n   * 切换流程：\n   * 1. 用户登录状态检查\n   * 2. 当前团队状态判断（避免重复切换）\n   * 3. 调用后端团队选择API\n   * 4. 验证切换结果\n   * 5. 更新本地Token和全局状态\n   * 6. 记录用户选择历史\n   * 7. 跳转到团队仪表盘\n   *\n   * 状态管理：\n   * - 设置切换加载状态（防止重复点击）\n   * - 更新全局用户和团队状态\n   * - 处理切换过程中的错误状态\n   *\n   * 错误处理：\n   * - 网络错误：显示网络连接提示\n   * - 权限错误：由响应拦截器统一处理\n   * - 业务错误：显示具体的错误信息\n   *\n   * @param teamId 要切换到的团队ID\n   * @param teamName 团队名称（用于显示消息）\n   */\n  const handleTeamSwitch = async (teamId: number, teamName: string) => {\n    /**\n     * 用户登录状态检查\n     *\n     * 确保用户已登录才能进行团队切换操作。\n     * 虽然组件层面已有登录检查，但这里再次确认以确保安全性。\n     */\n    if (!initialState?.currentUser) {\n      return;\n    }\n\n    try {\n      /**\n       * 设置切换状态\n       *\n       * 标记当前正在切换的团队ID，用于：\n       * 1. 在UI上显示加载状态\n       * 2. 防止用户重复点击\n       * 3. 提供视觉反馈\n       */\n      setSwitchingTeamId(teamId);\n\n      /**\n       * 当前团队检查\n       *\n       * 如果用户点击的是当前已选择的团队，直接跳转到仪表盘，\n       * 避免不必要的API调用和Token更新。\n       */\n      if (teamId === actualCurrentTeamId) {\n        history.push('/dashboard');\n        return;\n      }\n\n      /**\n       * 执行团队切换API调用\n       *\n       * 调用后端的团队选择接口，后端会：\n       * 1. 验证用户是否有权限访问该团队\n       * 2. 生成包含新团队信息的JWT Token\n       * 3. 返回团队详细信息和切换状态\n       */\n      const response = await AuthService.selectTeam({ teamId });\n\n      /**\n       * 验证切换结果\n       *\n       * 检查后端返回的响应是否表示切换成功：\n       * - teamSelectionSuccess: 切换成功标识\n       * - team: 新团队的详细信息\n       * - team.id: 确认返回的团队ID与请求的一致\n       */\n      if (\n        response.teamSelectionSuccess &&\n        response.team &&\n        response.team.id === teamId\n      ) {\n        /**\n         * 记录用户选择历史\n         *\n         * 将用户的团队选择记录到本地存储，用于：\n         * - 下次登录时的默认团队选择\n         * - 用户行为分析\n         * - 提升用户体验\n         */\n        if (currentUserId) {\n          recordTeamSelection(currentUserId, teamId);\n        }\n\n        /**\n         * 异步更新全局状态\n         *\n         * 由于Token已经更新，需要同步更新全局状态中的用户和团队信息。\n         * 使用异步更新避免阻塞页面跳转，提升用户体验。\n         *\n         * 更新流程：\n         * 1. 并行获取最新的用户信息和团队信息\n         * 2. 验证获取的团队信息是否正确\n         * 3. 更新全局状态\n         * 4. 处理更新过程中的错误\n         */\n        if (\n          initialState?.fetchTeamInfo &&\n          initialState?.fetchUserInfo &&\n          setInitialState\n        ) {\n          // 异步更新状态，不阻塞跳转\n          Promise.all([\n            initialState.fetchUserInfo(),\n            initialState.fetchTeamInfo(),\n          ])\n            .then(([currentUser, currentTeam]) => {\n              // 确认获取的团队信息与切换的团队一致\n              if (currentTeam && currentTeam.id === teamId) {\n                setInitialState({\n                  ...initialState,\n                  currentUser,\n                  currentTeam,\n                });\n              }\n            })\n            .catch((error) => {\n              console.error('更新 initialState 失败:', error);\n              // 状态更新失败不影响团队切换的核心功能\n            });\n        }\n\n        /**\n         * 页面跳转\n         *\n         * 切换成功后跳转到团队仪表盘。\n         * 路由守卫会验证新的Token并允许访问团队页面。\n         */\n        history.push('/dashboard');\n      } else {\n        /**\n         * 切换失败处理\n         *\n         * 如果后端返回的响应不符合预期，说明切换失败。\n         * 记录错误日志并提示用户重试。\n         */\n        // 团队切换响应异常，未返回正确的团队信息\n      }\n    } catch (error: any) {\n      /**\n       * 异常处理\n       *\n       * 处理团队切换过程中可能出现的各种异常：\n       * - 网络错误：连接超时、服务器不可达等\n       * - 权限错误：用户无权限访问该团队\n       * - 业务错误：团队不存在、状态异常等\n       *\n       * 错误处理策略：\n       * 1. 记录详细的错误日志用于调试\n       * 2. 响应拦截器已处理大部分错误消息\n       * 3. 只对网络错误显示通用提示\n       */\n      // 错误处理由响应拦截器统一处理\n    } finally {\n      /**\n       * 清理切换状态\n       *\n       * 无论切换成功还是失败，都要清除切换状态，\n       * 恢复UI的正常状态，允许用户进行下一次操作。\n       */\n      setSwitchingTeamId(null);\n    }\n  };\n\n  /**\n   * 处理团队管理\n   */\n  const handleTeamManagement = async (team: TeamDetailResponse) => {\n    try {\n      // 先切换到目标团队以确保有正确的权限\n      await AuthService.selectTeam({ teamId: team.id });\n      setSelectedTeam(team);\n      setTeamManagementModalVisible(true);\n    } catch (error) {\n      console.error('切换团队失败:', error);\n      message.error('无法打开团队管理');\n    }\n  };\n\n  /**\n   * 处理退出团队\n   */\n  const handleLeaveTeam = (team: TeamDetailResponse) => {\n    setSelectedTeam(team);\n    setLeaveTeamModalVisible(true);\n  };\n\n  /**\n   * 确认退出团队\n   */\n  const confirmLeaveTeam = async () => {\n    if (!selectedTeam) return;\n\n    try {\n      // 先切换到目标团队\n      await AuthService.selectTeam({ teamId: selectedTeam.id });\n\n      // 退出团队\n      await TeamService.leaveTeam();\n\n      // 重新获取团队列表\n      await fetchTeams();\n\n      // 更新全局状态，清除当前团队\n      if (setInitialState) {\n        await setInitialState((prevState) => ({\n          ...prevState,\n          currentTeam: undefined,\n        }));\n      }\n\n      message.success('已成功退出团队');\n      setLeaveTeamModalVisible(false);\n      setSelectedTeam(null);\n    } catch (error) {\n      console.error('退出团队失败:', error);\n      message.error('退出团队失败');\n    }\n  };\n\n\n\n  return (\n    <>\n      {/* 注入样式 */}\n      <style dangerouslySetInnerHTML={{ __html: styles }} />\n\n      <ProCard\n        title=\"团队列表\"\n        style={{\n          borderRadius: 8,\n          marginBottom: 16,\n        }}\n        headStyle={{\n          borderBottom: '1px solid #f0f0f0',\n          paddingBottom: 12,\n        }}\n        bodyStyle={{\n          padding: '16px',\n        }}\n      >\n        {error ? (\n          <Alert\n            message=\"团队列表加载失败\"\n            description={error}\n            type=\"error\"\n            showIcon\n            style={{ marginBottom: 16 }}\n          />\n        ) : (\n          <Spin spinning={loading}>\n            {!initialState?.currentUser ? (\n              <div style={{ textAlign: 'center', padding: '40px 20px' }}>\n                <Text type=\"secondary\">请先登录以查看团队列表</Text>\n              </div>\n            ) : teams.length === 0 && !loading ? (\n              <div style={{ textAlign: 'center', padding: '40px 20px' }}>\n                <Text type=\"secondary\">暂无团队，请先加入或创建团队</Text>\n              </div>\n            ) : (\n              <ProList\n                dataSource={teams}\n                renderItem={(item) => (\n                  <ProCard\n                    className=\"team-item\"\n                    style={{\n                        background:\n                          actualCurrentTeamId === item.id\n                            ? 'linear-gradient(135deg, #f0f9ff, #e6f4ff)'\n                            : '#fff',\n                        borderRadius: 8,\n                        boxShadow:\n                          actualCurrentTeamId === item.id\n                            ? '0 2px 8px rgba(24, 144, 255, 0.12)'\n                            : '0 1px 4px rgba(0,0,0,0.06)',\n                        width: '100%',\n                        borderLeft: `3px solid ${item.isCreator ? '#722ed1' : '#52c41a'}`,\n                        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n                        border:\n                          actualCurrentTeamId === item.id\n                            ? '1px solid #91caff'\n                            : '1px solid #f0f0f0',\n                        padding: '12px 16px',\n                        position: 'relative',\n                        overflow: 'hidden',\n                      }}\n                      hoverable\n                      onMouseEnter={(e) => {\n                        if (actualCurrentTeamId !== item.id) {\n                          e.currentTarget.style.transform = 'translateY(-2px)';\n                          e.currentTarget.style.boxShadow =\n                            '0 8px 24px rgba(0,0,0,0.12)';\n                        }\n                      }}\n                      onMouseLeave={(e) => {\n                        if (actualCurrentTeamId !== item.id) {\n                          e.currentTarget.style.transform = 'translateY(0)';\n                          e.currentTarget.style.boxShadow =\n                            '0 2px 8px rgba(0,0,0,0.06)';\n                        }\n                      }}\n                    >\n                      {/* 响应式布局 */}\n                      <Row\n                        gutter={[8, 8]}\n                        align=\"middle\"\n                        style={{ width: '100%' }}\n                      >\n                        {/* 左侧：团队信息 */}\n                        <Col xs={24} sm={24} md={14} lg={12} xl={14}>\n                          <Flex vertical gap={6} className=\"team-info-wrap\">\n                            {/* 团队名称行 */}\n                            <Flex align=\"center\" gap={8} wrap=\"wrap\">\n                              <div\n                                style={{\n                                  cursor: 'pointer',\n                                  padding: '2px 4px',\n                                  borderRadius: 4,\n                                  transition: 'all 0.2s ease',\n                                  display: 'flex',\n                                  alignItems: 'center',\n                                  gap: 6,\n                                }}\n                                onClick={() =>\n                                  handleTeamSwitch(item.id, item.name)\n                                }\n                                onMouseEnter={(e) => {\n                                  e.currentTarget.style.background =\n                                    'rgba(24, 144, 255, 0.05)';\n                                }}\n                                onMouseLeave={(e) => {\n                                  e.currentTarget.style.background =\n                                    'transparent';\n                                }}\n                              >\n                                <Text\n                                  strong\n                                  style={{\n                                    fontSize: 16,\n                                    color:\n                                      actualCurrentTeamId === item.id\n                                        ? '#1890ff'\n                                        : '#262626',\n                                    lineHeight: 1.2,\n                                  }}\n                                >\n                                  {item.name}\n                                </Text>\n                                <RightOutlined\n                                  style={{\n                                    fontSize: 10,\n                                    color:\n                                      actualCurrentTeamId === item.id\n                                        ? '#1890ff'\n                                        : '#8c8c8c',\n                                    verticalAlign: 'middle',\n                                    display: 'inline-flex',\n                                    alignItems: 'center',\n                                  }}\n                                />\n                              </div>\n\n                              {/* 状态标识 */}\n                              {actualCurrentTeamId === item.id && (\n                                <span\n                                  style={{\n                                    background: '#1890ff',\n                                    color: 'white',\n                                    padding: '1px 6px',\n                                    borderRadius: 8,\n                                    fontSize: 10,\n                                    fontWeight: 500,\n                                  }}\n                                >\n                                  当前\n                                </span>\n                              )}\n\n\n\n                              {switchingTeamId === item.id && (\n                                <Flex align=\"center\" gap={4}>\n                                  <Spin size=\"small\" />\n                                  <Text style={{ fontSize: 10, color: '#666' }}>\n                                    切换中\n                                  </Text>\n                                </Flex>\n                              )}\n                            </Flex>\n\n                            {/* 团队基本信息 */}\n                            <Flex align=\"center\" gap={12} wrap=\"wrap\" className=\"team-meta-info\">\n                              <Tooltip\n                                title={`团队创建时间: ${new Date(item.createdAt).toLocaleString('zh-CN')}`}\n                              >\n                                <Flex align=\"center\" gap={4}>\n                                  <ClockCircleOutlined\n                                    style={{ color: '#8c8c8c', fontSize: 12 }}\n                                  />\n                                  <Text\n                                    style={{ fontSize: 12, color: '#8c8c8c' }}\n                                  >\n                                    创建: {new Date(\n                                      item.createdAt,\n                                    ).toLocaleDateString('zh-CN')}\n                                  </Text>\n                                </Flex>\n                              </Tooltip>\n\n                              {/* 加入日期 */}\n                              {item.assignedAt && (\n                                <Tooltip\n                                  title={`加入团队时间: ${new Date(item.assignedAt).toLocaleString('zh-CN')}`}\n                                >\n                                  <Flex align=\"center\" gap={4}>\n                                    <UserOutlined\n                                      style={{ color: '#8c8c8c', fontSize: 12 }}\n                                    />\n                                    <Text\n                                      style={{ fontSize: 12, color: '#8c8c8c' }}\n                                    >\n                                      加入: {new Date(\n                                        item.assignedAt,\n                                      ).toLocaleDateString('zh-CN')}\n                                    </Text>\n                                  </Flex>\n                                </Tooltip>\n                              )}\n\n                              <Tooltip\n                                title={`团队成员: ${item.memberCount}人`}\n                              >\n                                <Flex align=\"center\" gap={4}>\n                                  <TeamOutlined\n                                    style={{ color: '#8c8c8c', fontSize: 12 }}\n                                  />\n                                  <Text\n                                    style={{ fontSize: 12, color: '#8c8c8c' }}\n                                  >\n                                    {item.memberCount} 人\n                                  </Text>\n                                </Flex>\n                              </Tooltip>\n                            </Flex>\n\n                            {/* 状态标识行 */}\n                            <Flex align=\"center\" gap={8} wrap=\"wrap\" className=\"team-status-badges\">\n                              {/* 角色标识 */}\n                              <span\n                                style={{\n                                  background: item.isCreator\n                                    ? '#722ed1'\n                                    : '#52c41a',\n                                  color: 'white',\n                                  padding: '2px 6px',\n                                  borderRadius: 8,\n                                  fontSize: 10,\n                                  fontWeight: 500,\n                                  display: 'flex',\n                                  alignItems: 'center',\n                                  gap: 2,\n                                }}\n                              >\n                                {item.isCreator ? (\n                                  <>\n                                    <CrownOutlined style={{ fontSize: 9 }} />\n                                    管理员\n                                  </>\n                                ) : (\n                                  <>\n                                    <UserOutlined style={{ fontSize: 9 }} />\n                                    成员\n                                  </>\n                                )}\n                              </span>\n\n                              {/* 用户状态标识 */}\n                              <span\n                                style={{\n                                  background: item.isActive ? '#52c41a' : '#ff4d4f',\n                                  color: 'white',\n                                  padding: '2px 6px',\n                                  borderRadius: 8,\n                                  fontSize: 10,\n                                  fontWeight: 500,\n                                  display: 'flex',\n                                  alignItems: 'center',\n                                  gap: 2,\n                                }}\n                              >\n                                {item.isActive ? (\n                                  <>\n                                    <CheckCircleOutlined style={{ fontSize: 9 }} />\n                                    启用\n                                  </>\n                                ) : (\n                                  <>\n                                    <MinusCircleOutlined style={{ fontSize: 9 }} />\n                                    停用\n                                  </>\n                                )}\n                              </span>\n\n                              {/* 操作按钮 - 移动到状态信息之后 */}\n                              {item.isCreator ? (\n                                <Tooltip title=\"团队管理\">\n                                  <Button\n                                    type=\"text\"\n                                    size=\"small\"\n                                    icon={<SettingOutlined style={{ fontSize: 12 }} />}\n                                    onClick={(e) => {\n                                      e.stopPropagation();\n                                      handleTeamManagement(item);\n                                    }}\n                                    style={{\n                                      color: '#722ed1',\n                                      display: 'flex',\n                                      alignItems: 'center',\n                                      justifyContent: 'center',\n                                      width: 24,\n                                      height: 24,\n                                      padding: 0,\n                                      borderRadius: 4,\n                                      border: '1px solid #d3adf7',\n                                      background: 'rgba(114, 46, 209, 0.04)',\n                                      transition: 'all 0.2s ease',\n                                    }}\n                                    onMouseEnter={(e) => {\n                                      e.currentTarget.style.background = 'rgba(114, 46, 209, 0.1)';\n                                      e.currentTarget.style.borderColor = '#722ed1';\n                                    }}\n                                    onMouseLeave={(e) => {\n                                      e.currentTarget.style.background = 'rgba(114, 46, 209, 0.04)';\n                                      e.currentTarget.style.borderColor = '#d3adf7';\n                                    }}\n                                  />\n                                </Tooltip>\n                              ) : (\n                                <Tooltip title=\"退出团队\">\n                                  <Button\n                                    type=\"text\"\n                                    size=\"small\"\n                                    icon={<LogoutOutlined style={{ fontSize: 12 }} />}\n                                    onClick={(e) => {\n                                      e.stopPropagation();\n                                      handleLeaveTeam(item);\n                                    }}\n                                    style={{\n                                      color: '#ff4d4f',\n                                      display: 'flex',\n                                      alignItems: 'center',\n                                      justifyContent: 'center',\n                                      width: 24,\n                                      height: 24,\n                                      padding: 0,\n                                      borderRadius: 4,\n                                      border: '1px solid #ffccc7',\n                                      background: 'rgba(255, 77, 79, 0.04)',\n                                      transition: 'all 0.2s ease',\n                                    }}\n                                    onMouseEnter={(e) => {\n                                      e.currentTarget.style.background = 'rgba(255, 77, 79, 0.1)';\n                                      e.currentTarget.style.borderColor = '#ff4d4f';\n                                    }}\n                                    onMouseLeave={(e) => {\n                                      e.currentTarget.style.background = 'rgba(255, 77, 79, 0.04)';\n                                      e.currentTarget.style.borderColor = '#ffccc7';\n                                    }}\n                                  />\n                                </Tooltip>\n                              )}\n                            </Flex>\n                          </Flex>\n                        </Col>\n\n                        {/* 右侧：响应式指标卡片和操作按钮 */}\n                        <Col xs={24} sm={24} md={10} lg={12} xl={10}>\n                          <Row\n                            gutter={[4, 4]}\n                            justify={{ xs: 'start', md: 'end' }}\n                            align=\"middle\"\n                          >\n                            {/* 车辆资源 */}\n                            <Col xs={6} sm={6} md={6} lg={6} xl={6}>\n                              <div\n                                style={{\n                                  background: '#f0f7ff',\n                                  border: '1px solid #d9e8ff',\n                                  borderRadius: 6,\n                                  padding: '4px 6px',\n                                  textAlign: 'center',\n                                  minWidth: '45px',\n                                }}\n                              >\n                                <Flex vertical align=\"center\" gap={1}>\n                                  <CarOutlined\n                                    style={{ color: '#1890ff', fontSize: 12 }}\n                                  />\n                                  <Text\n                                    strong\n                                    style={{\n                                      fontSize: 14,\n                                      color: '#1890ff',\n                                      lineHeight: 1,\n                                    }}\n                                  >\n                                    {item.stats?.vehicles || 0}\n                                  </Text>\n                                  <Text style={{ fontSize: 8, color: '#666' }}>\n                                    车辆\n                                  </Text>\n                                </Flex>\n                              </div>\n                            </Col>\n\n                            {/* 人员资源 */}\n                            <Col xs={6} sm={6} md={6} lg={6} xl={6}>\n                              <div\n                                style={{\n                                  background: '#f6ffed',\n                                  border: '1px solid #d1f0be',\n                                  borderRadius: 6,\n                                  padding: '4px 6px',\n                                  textAlign: 'center',\n                                  minWidth: '45px',\n                                }}\n                              >\n                                <Flex vertical align=\"center\" gap={1}>\n                                  <UserOutlined\n                                    style={{ color: '#52c41a', fontSize: 12 }}\n                                  />\n                                  <Text\n                                    strong\n                                    style={{\n                                      fontSize: 14,\n                                      color: '#52c41a',\n                                      lineHeight: 1,\n                                    }}\n                                  >\n                                    {item.stats?.personnel || 0}\n                                  </Text>\n                                  <Text style={{ fontSize: 8, color: '#666' }}>\n                                    人员\n                                  </Text>\n                                </Flex>\n                              </div>\n                            </Col>\n\n                            {/* 临期事项 */}\n                            <Col xs={6} sm={6} md={6} lg={6} xl={6}>\n                              <div\n                                style={{\n                                  background: '#fff7e6',\n                                  border: '1px solid #ffd666',\n                                  borderRadius: 6,\n                                  padding: '4px 6px',\n                                  textAlign: 'center',\n                                  minWidth: '45px',\n                                }}\n                              >\n                                <Flex vertical align=\"center\" gap={1}>\n                                  <ExclamationCircleOutlined\n                                    style={{ color: '#faad14', fontSize: 12 }}\n                                  />\n                                  <Text\n                                    strong\n                                    style={{\n                                      fontSize: 14,\n                                      color: '#faad14',\n                                      lineHeight: 1,\n                                    }}\n                                  >\n                                    {item.stats?.expiring || 0}\n                                  </Text>\n                                  <Text style={{ fontSize: 8, color: '#666' }}>\n                                    临期\n                                  </Text>\n                                </Flex>\n                              </div>\n                            </Col>\n\n                            {/* 逾期事项 */}\n                            <Col xs={6} sm={6} md={6} lg={6} xl={6}>\n                              <div\n                                style={{\n                                  background: '#fff1f0',\n                                  border: '1px solid #ffccc7',\n                                  borderRadius: 6,\n                                  padding: '4px 6px',\n                                  textAlign: 'center',\n                                  minWidth: '45px',\n                                }}\n                              >\n                                <Flex vertical align=\"center\" gap={1}>\n                                  <ExclamationCircleOutlined\n                                    style={{ color: '#ff4d4f', fontSize: 12 }}\n                                  />\n                                  <Text\n                                    strong\n                                    style={{\n                                      fontSize: 14,\n                                      color: '#ff4d4f',\n                                      lineHeight: 1,\n                                    }}\n                                  >\n                                    {item.stats?.overdue || 0}\n                                  </Text>\n                                  <Text style={{ fontSize: 8, color: '#666' }}>\n                                    逾期\n                                  </Text>\n                                </Flex>\n                              </div>\n                            </Col>\n\n                            {/* 操作按钮 */}\n                            <Col xs={12} sm={8} md={6} lg={4} xl={4}>\n                              <Flex justify=\"end\" align=\"center\" gap={6}>\n                                {item.isCreator ? (\n                                  <Tooltip title=\"团队管理\">\n                                    <Button\n                                      type=\"text\"\n                                      size=\"small\"\n                                      icon={<SettingOutlined style={{ fontSize: 14 }} />}\n                                      onClick={(e) => {\n                                        e.stopPropagation();\n                                        handleTeamManagement(item);\n                                      }}\n                                      style={{\n                                        color: '#722ed1',\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        justifyContent: 'center',\n                                        width: 32,\n                                        height: 32,\n                                        padding: 0,\n                                        borderRadius: 6,\n                                        border: '1px solid #d3adf7',\n                                        background: 'rgba(114, 46, 209, 0.04)',\n                                        transition: 'all 0.2s ease',\n                                      }}\n                                      onMouseEnter={(e) => {\n                                        e.currentTarget.style.background = 'rgba(114, 46, 209, 0.1)';\n                                        e.currentTarget.style.borderColor = '#722ed1';\n                                      }}\n                                      onMouseLeave={(e) => {\n                                        e.currentTarget.style.background = 'rgba(114, 46, 209, 0.04)';\n                                        e.currentTarget.style.borderColor = '#d3adf7';\n                                      }}\n                                    />\n                                  </Tooltip>\n                                ) : (\n                                  <Tooltip title=\"退出团队\">\n                                    <Button\n                                      type=\"text\"\n                                      size=\"small\"\n                                      icon={<LogoutOutlined style={{ fontSize: 14 }} />}\n                                      onClick={(e) => {\n                                        e.stopPropagation();\n                                        handleLeaveTeam(item);\n                                      }}\n                                      style={{\n                                        color: '#ff4d4f',\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        justifyContent: 'center',\n                                        width: 32,\n                                        height: 32,\n                                        padding: 0,\n                                        borderRadius: 6,\n                                        border: '1px solid #ffccc7',\n                                        background: 'rgba(255, 77, 79, 0.04)',\n                                        transition: 'all 0.2s ease',\n                                      }}\n                                      onMouseEnter={(e) => {\n                                        e.currentTarget.style.background = 'rgba(255, 77, 79, 0.1)';\n                                        e.currentTarget.style.borderColor = '#ff4d4f';\n                                      }}\n                                      onMouseLeave={(e) => {\n                                        e.currentTarget.style.background = 'rgba(255, 77, 79, 0.04)';\n                                        e.currentTarget.style.borderColor = '#ffccc7';\n                                      }}\n                                    />\n                                  </Tooltip>\n                                )}\n                              </Flex>\n                            </Col>\n                          </Row>\n                        </Col>\n                      </Row>\n                    </ProCard>\n                )}\n              />\n            )}\n          </Spin>\n        )}\n      </ProCard>\n\n      {/* 团队管理模态框 */}\n      <TeamManagementModal\n        visible={teamManagementModalVisible}\n        onCancel={() => {\n          setTeamManagementModalVisible(false);\n          setSelectedTeam(null);\n        }}\n        team={selectedTeam}\n        onRefresh={fetchTeams}\n      />\n\n      {/* 退出团队确认模态框 */}\n      <Modal\n        title=\"退出团队\"\n        open={leaveTeamModalVisible}\n        onCancel={() => {\n          setLeaveTeamModalVisible(false);\n          setSelectedTeam(null);\n        }}\n        footer={[\n          <Button\n            key=\"cancel\"\n            onClick={() => {\n              setLeaveTeamModalVisible(false);\n              setSelectedTeam(null);\n            }}\n          >\n            取消\n          </Button>,\n          <Button\n            key=\"leave\"\n            type=\"primary\"\n            danger\n            onClick={confirmLeaveTeam}\n          >\n            确认退出\n          </Button>,\n        ]}\n      >\n        <div style={{ textAlign: 'center', padding: '20px 0' }}>\n          <p>确定要退出团队 <strong>{selectedTeam?.name}</strong> 吗？</p>\n          <p style={{ color: '#ff4d4f' }}>退出后您将无法访问该团队的资源和数据</p>\n        </div>\n      </Modal>\n\n      {/* 创建团队功能已移至设置页面 */}\n    </>\n  );\n};\n\nexport default TeamListCard;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uCACA;IACE,SAAS;;;;;;wCCymCb;;;2BAAA;;;;;;;0CAhmCO;wCAC2B;yCAY3B;kDAC0B;oFACU;6CACf;yCACA;+CAMrB;uDACkD;iGACzB;;;;;;;;;;YAEhC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,gBAAU;YAKlC,UAAU;YACV,MAAM,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkEhB,CAAC;YAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA8BC,GACD,MAAM,eAAyB;;gBAC7B;;;;;;;;GAQC,GACD,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,eAAQ,EAAuB,EAAE;gBAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;gBACvC,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,eAAQ,EAAgB;gBAClD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,eAAQ,EAAgB;gBAEtE,UAAU;gBACV,MAAM,CAAC,4BAA4B,8BAA8B,GAAG,IAAA,eAAQ,EAAC;gBAC7E,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,IAAA,eAAQ,EAAC;gBACnE,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,eAAQ,EAA4B;gBAE5E;;;;;;GAMC,GAED;;;;;;;GAOC,GACD,MAAM,EAAE,YAAY,EAAE,eAAe,EAAE,GAAG,IAAA,aAAQ,EAAC;gBACnD,MAAM,cAAc,yBAAA,mCAAA,aAAc,WAAW;gBAE7C;;;;;;;;;;;;GAYC,GACD,MAAM,qBAAqB,IAAA,qCAAyB;gBACpD,MAAM,gBAAgB,IAAA,qCAAyB;gBAC/C,MAAM,iBAAiB,IAAA,iCAAqB;gBAE5C,gBAAgB;gBAChB,8BAA8B;gBAC9B,qCAAqC;gBACrC,uBAAuB;gBACvB,gCAAgC;gBAChC,MAAM,qBAAqB,CAAC,CAC1B,CAAA,kBACA,sBACA,eACA,YAAY,EAAE,KAAK,sBACnB,iBACA,IAAA,uCAAmB,EAAC,eAAe,mBAAkB;gBAGvD,wCAAwC;gBACxC,MAAM,sBAAsB,qBAAqB,qBAAqB;gBAEtE,OAAO;gBACP,QAAQ,GAAG,CAAC,sBAAsB;oBAChC,WAAW,EAAE,wBAAA,kCAAA,YAAa,EAAE;oBAC5B;oBACA;oBACA;oBACA;oBACA;oBACA,4BAA4B,iBAAiB,qBAAqB,IAAA,uCAAmB,EAAC,eAAe,sBAAsB;oBAC3H,yBAAyB,CAAC,EAAC,yBAAA,mCAAA,aAAc,WAAW;gBACtD;gBAEA,WAAW;gBACX,MAAM,aAAa;oBACjB,IAAI;wBACF,WAAW;wBACX,SAAS;wBACT,MAAM,YAAY,MAAM,iBAAW,CAAC,qBAAqB;wBACzD,SAAS;oBACX,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,aAAa;wBAC3B,SAAS;oBACX,SAAU;wBACR,WAAW;oBACb;gBACF;gBAEA,IAAA,gBAAS,EAAC;oBACR,mBAAmB;oBACnB,IAAI,yBAAA,mCAAA,aAAc,WAAW,EAC3B;gBAEJ,GAAG;oBAAC,yBAAA,mCAAA,aAAc,WAAW;iBAAC;gBAE9B,mBAAmB;gBACnB,IAAA,gBAAS,EAAC;oBACR,4CAA4C;oBAC5C,IAAI,EAAC,yBAAA,mCAAA,aAAc,WAAW,GAAE;wBAC9B,SAAS,EAAE;wBACX,SAAS;wBACT,WAAW;wBACX,mBAAmB;oBACrB;gBACF,GAAG;oBAAC,yBAAA,mCAAA,aAAc,WAAW;iBAAC;gBAE9B,aAAa;gBACb,IAAA,gBAAS,EAAC;oBACR,QAAQ,GAAG,CAAC,aAAa;wBACvB,WAAW,EAAE,wBAAA,kCAAA,YAAa,EAAE;wBAC5B;wBACA;oBACF;gBACF,GAAG;oBAAC,wBAAA,kCAAA,YAAa,EAAE;oBAAE;oBAAqB;iBAAmB;gBAE7D,2BAA2B;gBAE3B;;;;;;;;;;;;;;;;;;;;;;;;;;;GA2BC,GACD,MAAM,mBAAmB,OAAO,QAAgB;oBAC9C;;;;;KAKC,GACD,IAAI,EAAC,yBAAA,mCAAA,aAAc,WAAW,GAC5B;oBAGF,IAAI;wBACF;;;;;;;OAOC,GACD,mBAAmB;wBAEnB;;;;;OAKC,GACD,IAAI,WAAW,qBAAqB;4BAClC,YAAO,CAAC,IAAI,CAAC;4BACb;wBACF;wBAEA;;;;;;;OAOC,GACD,MAAM,WAAW,MAAM,qBAAW,CAAC,UAAU,CAAC;4BAAE;wBAAO;wBAEvD;;;;;;;OAOC,GACD,IACE,SAAS,oBAAoB,IAC7B,SAAS,IAAI,IACb,SAAS,IAAI,CAAC,EAAE,KAAK,QACrB;4BACA;;;;;;;SAOC,GACD,IAAI,eACF,IAAA,uCAAmB,EAAC,eAAe;4BAGrC;;;;;;;;;;;SAWC,GACD,IACE,CAAA,yBAAA,mCAAA,aAAc,aAAa,MAC3B,yBAAA,mCAAA,aAAc,aAAa,KAC3B,iBAEA,eAAe;4BACf,QAAQ,GAAG,CAAC;gCACV,aAAa,aAAa;gCAC1B,aAAa,aAAa;6BAC3B,EACE,IAAI,CAAC,CAAC,CAAC,aAAa,YAAY;gCAC/B,oBAAoB;gCACpB,IAAI,eAAe,YAAY,EAAE,KAAK,QACpC,gBAAgB;oCACd,GAAG,YAAY;oCACf;oCACA;gCACF;4BAEJ,GACC,KAAK,CAAC,CAAC;gCACN,QAAQ,KAAK,CAAC,uBAAuB;4BACrC,qBAAqB;4BACvB;4BAGJ;;;;;SAKC,GACD,YAAO,CAAC,IAAI,CAAC;wBACf;oBASF,EAAE,OAAO,OAAY;oBACnB;;;;;;;;;;;;OAYC,GACD,iBAAiB;oBACnB,SAAU;wBACR;;;;;OAKC,GACD,mBAAmB;oBACrB;gBACF;gBAEA;;GAEC,GACD,MAAM,uBAAuB,OAAO;oBAClC,IAAI;wBACF,oBAAoB;wBACpB,MAAM,qBAAW,CAAC,UAAU,CAAC;4BAAE,QAAQ,KAAK,EAAE;wBAAC;wBAC/C,gBAAgB;wBAChB,8BAA8B;oBAChC,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,WAAW;wBACzB,aAAO,CAAC,KAAK,CAAC;oBAChB;gBACF;gBAEA;;GAEC,GACD,MAAM,kBAAkB,CAAC;oBACvB,gBAAgB;oBAChB,yBAAyB;gBAC3B;gBAEA;;GAEC,GACD,MAAM,mBAAmB;oBACvB,IAAI,CAAC,cAAc;oBAEnB,IAAI;wBACF,WAAW;wBACX,MAAM,qBAAW,CAAC,UAAU,CAAC;4BAAE,QAAQ,aAAa,EAAE;wBAAC;wBAEvD,OAAO;wBACP,MAAM,iBAAW,CAAC,SAAS;wBAE3B,WAAW;wBACX,MAAM;wBAEN,gBAAgB;wBAChB,IAAI,iBACF,MAAM,gBAAgB,CAAC,YAAe,CAAA;gCACpC,GAAG,SAAS;gCACZ,aAAa;4BACf,CAAA;wBAGF,aAAO,CAAC,OAAO,CAAC;wBAChB,yBAAyB;wBACzB,gBAAgB;oBAClB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,WAAW;wBACzB,aAAO,CAAC,KAAK,CAAC;oBAChB;gBACF;gBAIA,qBACE;;sCAEE,2BAAC;4BAAM,yBAAyB;gCAAE,QAAQ;4BAAO;;;;;;sCAEjD,2BAAC,sBAAO;4BACN,OAAM;4BACN,OAAO;gCACL,cAAc;gCACd,cAAc;4BAChB;4BACA,WAAW;gCACT,cAAc;gCACd,eAAe;4BACjB;4BACA,WAAW;gCACT,SAAS;4BACX;sCAEC,sBACC,2BAAC,WAAK;gCACJ,SAAQ;gCACR,aAAa;gCACb,MAAK;gCACL,QAAQ;gCACR,OAAO;oCAAE,cAAc;gCAAG;;;;;qDAG5B,2BAAC,UAAI;gCAAC,UAAU;0CACb,EAAC,yBAAA,mCAAA,aAAc,WAAW,kBACzB,2BAAC;oCAAI,OAAO;wCAAE,WAAW;wCAAU,SAAS;oCAAY;8CACtD,cAAA,2BAAC;wCAAK,MAAK;kDAAY;;;;;;;;;;2CAEvB,MAAM,MAAM,KAAK,KAAK,CAAC,wBACzB,2BAAC;oCAAI,OAAO;wCAAE,WAAW;wCAAU,SAAS;oCAAY;8CACtD,cAAA,2BAAC;wCAAK,MAAK;kDAAY;;;;;;;;;;yDAGzB,2BAAC,sBAAO;oCACN,YAAY;oCACZ,YAAY,CAAC;4CAwVQ,aAiCA,cAiCA,cAiCA;6DA1bnB,2BAAC,sBAAO;4CACN,WAAU;4CACV,OAAO;gDACH,YACE,wBAAwB,KAAK,EAAE,GAC3B,8CACA;gDACN,cAAc;gDACd,WACE,wBAAwB,KAAK,EAAE,GAC3B,uCACA;gDACN,OAAO;gDACP,YAAY,CAAC,UAAU,EAAE,KAAK,SAAS,GAAG,YAAY,UAAU,CAAC;gDACjE,YAAY;gDACZ,QACE,wBAAwB,KAAK,EAAE,GAC3B,sBACA;gDACN,SAAS;gDACT,UAAU;gDACV,UAAU;4CACZ;4CACA,SAAS;4CACT,cAAc,CAAC;gDACb,IAAI,wBAAwB,KAAK,EAAE,EAAE;oDACnC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;oDAClC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAC7B;gDACJ;4CACF;4CACA,cAAc,CAAC;gDACb,IAAI,wBAAwB,KAAK,EAAE,EAAE;oDACnC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;oDAClC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAC7B;gDACJ;4CACF;sDAGA,cAAA,2BAAC,SAAG;gDACF,QAAQ;oDAAC;oDAAG;iDAAE;gDACd,OAAM;gDACN,OAAO;oDAAE,OAAO;gDAAO;;kEAGvB,2BAAC,SAAG;wDAAC,IAAI;wDAAI,IAAI;wDAAI,IAAI;wDAAI,IAAI;wDAAI,IAAI;kEACvC,cAAA,2BAAC,UAAI;4DAAC,QAAQ;4DAAC,KAAK;4DAAG,WAAU;;8EAE/B,2BAAC,UAAI;oEAAC,OAAM;oEAAS,KAAK;oEAAG,MAAK;;sFAChC,2BAAC;4EACC,OAAO;gFACL,QAAQ;gFACR,SAAS;gFACT,cAAc;gFACd,YAAY;gFACZ,SAAS;gFACT,YAAY;gFACZ,KAAK;4EACP;4EACA,SAAS,IACP,iBAAiB,KAAK,EAAE,EAAE,KAAK,IAAI;4EAErC,cAAc,CAAC;gFACb,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAC9B;4EACJ;4EACA,cAAc,CAAC;gFACb,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAC9B;4EACJ;;8FAEA,2BAAC;oFACC,MAAM;oFACN,OAAO;wFACL,UAAU;wFACV,OACE,wBAAwB,KAAK,EAAE,GAC3B,YACA;wFACN,YAAY;oFACd;8FAEC,KAAK,IAAI;;;;;;8FAEZ,2BAAC,oBAAa;oFACZ,OAAO;wFACL,UAAU;wFACV,OACE,wBAAwB,KAAK,EAAE,GAC3B,YACA;wFACN,eAAe;wFACf,SAAS;wFACT,YAAY;oFACd;;;;;;;;;;;;wEAKH,wBAAwB,KAAK,EAAE,kBAC9B,2BAAC;4EACC,OAAO;gFACL,YAAY;gFACZ,OAAO;gFACP,SAAS;gFACT,cAAc;gFACd,UAAU;gFACV,YAAY;4EACd;sFACD;;;;;;wEAOF,oBAAoB,KAAK,EAAE,kBAC1B,2BAAC,UAAI;4EAAC,OAAM;4EAAS,KAAK;;8FACxB,2BAAC,UAAI;oFAAC,MAAK;;;;;;8FACX,2BAAC;oFAAK,OAAO;wFAAE,UAAU;wFAAI,OAAO;oFAAO;8FAAG;;;;;;;;;;;;;;;;;;8EAQpD,2BAAC,UAAI;oEAAC,OAAM;oEAAS,KAAK;oEAAI,MAAK;oEAAO,WAAU;;sFAClD,2BAAC,aAAO;4EACN,OAAO,CAAC,QAAQ,EAAE,IAAI,KAAK,KAAK,SAAS,EAAE,cAAc,CAAC,SAAS,CAAC;sFAEpE,cAAA,2BAAC,UAAI;gFAAC,OAAM;gFAAS,KAAK;;kGACxB,2BAAC,0BAAmB;wFAClB,OAAO;4FAAE,OAAO;4FAAW,UAAU;wFAAG;;;;;;kGAE1C,2BAAC;wFACC,OAAO;4FAAE,UAAU;4FAAI,OAAO;wFAAU;;4FACzC;4FACM,IAAI,KACP,KAAK,SAAS,EACd,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;wEAM1B,KAAK,UAAU,kBACd,2BAAC,aAAO;4EACN,OAAO,CAAC,QAAQ,EAAE,IAAI,KAAK,KAAK,UAAU,EAAE,cAAc,CAAC,SAAS,CAAC;sFAErE,cAAA,2BAAC,UAAI;gFAAC,OAAM;gFAAS,KAAK;;kGACxB,2BAAC,mBAAY;wFACX,OAAO;4FAAE,OAAO;4FAAW,UAAU;wFAAG;;;;;;kGAE1C,2BAAC;wFACC,OAAO;4FAAE,UAAU;4FAAI,OAAO;wFAAU;;4FACzC;4FACM,IAAI,KACP,KAAK,UAAU,EACf,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;sFAM7B,2BAAC,aAAO;4EACN,OAAO,CAAC,MAAM,EAAE,KAAK,WAAW,CAAC,CAAC,CAAC;sFAEnC,cAAA,2BAAC,UAAI;gFAAC,OAAM;gFAAS,KAAK;;kGACxB,2BAAC,mBAAY;wFACX,OAAO;4FAAE,OAAO;4FAAW,UAAU;wFAAG;;;;;;kGAE1C,2BAAC;wFACC,OAAO;4FAAE,UAAU;4FAAI,OAAO;wFAAU;;4FAEvC,KAAK,WAAW;4FAAC;;;;;;;;;;;;;;;;;;;;;;;;8EAO1B,2BAAC,UAAI;oEAAC,OAAM;oEAAS,KAAK;oEAAG,MAAK;oEAAO,WAAU;;sFAEjD,2BAAC;4EACC,OAAO;gFACL,YAAY,KAAK,SAAS,GACtB,YACA;gFACJ,OAAO;gFACP,SAAS;gFACT,cAAc;gFACd,UAAU;gFACV,YAAY;gFACZ,SAAS;gFACT,YAAY;gFACZ,KAAK;4EACP;sFAEC,KAAK,SAAS,iBACb;;kGACE,2BAAC,oBAAa;wFAAC,OAAO;4FAAE,UAAU;wFAAE;;;;;;oFAAK;;6GAI3C;;kGACE,2BAAC,mBAAY;wFAAC,OAAO;4FAAE,UAAU;wFAAE;;;;;;oFAAK;;;;;;;;sFAO9C,2BAAC;4EACC,OAAO;gFACL,YAAY,KAAK,QAAQ,GAAG,YAAY;gFACxC,OAAO;gFACP,SAAS;gFACT,cAAc;gFACd,UAAU;gFACV,YAAY;gFACZ,SAAS;gFACT,YAAY;gFACZ,KAAK;4EACP;sFAEC,KAAK,QAAQ,iBACZ;;kGACE,2BAAC,0BAAmB;wFAAC,OAAO;4FAAE,UAAU;wFAAE;;;;;;oFAAK;;6GAIjD;;kGACE,2BAAC,0BAAmB;wFAAC,OAAO;4FAAE,UAAU;wFAAE;;;;;;oFAAK;;;;;;;;wEAOpD,KAAK,SAAS,iBACb,2BAAC,aAAO;4EAAC,OAAM;sFACb,cAAA,2BAAC,YAAM;gFACL,MAAK;gFACL,MAAK;gFACL,oBAAM,2BAAC,sBAAe;oFAAC,OAAO;wFAAE,UAAU;oFAAG;;;;;;gFAC7C,SAAS,CAAC;oFACR,EAAE,eAAe;oFACjB,qBAAqB;gFACvB;gFACA,OAAO;oFACL,OAAO;oFACP,SAAS;oFACT,YAAY;oFACZ,gBAAgB;oFAChB,OAAO;oFACP,QAAQ;oFACR,SAAS;oFACT,cAAc;oFACd,QAAQ;oFACR,YAAY;oFACZ,YAAY;gFACd;gFACA,cAAc,CAAC;oFACb,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG;oFACnC,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,GAAG;gFACtC;gFACA,cAAc,CAAC;oFACb,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG;oFACnC,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,GAAG;gFACtC;;;;;;;;;;mGAIJ,2BAAC,aAAO;4EAAC,OAAM;sFACb,cAAA,2BAAC,YAAM;gFACL,MAAK;gFACL,MAAK;gFACL,oBAAM,2BAAC,qBAAc;oFAAC,OAAO;wFAAE,UAAU;oFAAG;;;;;;gFAC5C,SAAS,CAAC;oFACR,EAAE,eAAe;oFACjB,gBAAgB;gFAClB;gFACA,OAAO;oFACL,OAAO;oFACP,SAAS;oFACT,YAAY;oFACZ,gBAAgB;oFAChB,OAAO;oFACP,QAAQ;oFACR,SAAS;oFACT,cAAc;oFACd,QAAQ;oFACR,YAAY;oFACZ,YAAY;gFACd;gFACA,cAAc,CAAC;oFACb,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG;oFACnC,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,GAAG;gFACtC;gFACA,cAAc,CAAC;oFACb,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG;oFACnC,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,GAAG;gFACtC;;;;;;;;;;;;;;;;;;;;;;;;;;;;kEASZ,2BAAC,SAAG;wDAAC,IAAI;wDAAI,IAAI;wDAAI,IAAI;wDAAI,IAAI;wDAAI,IAAI;kEACvC,cAAA,2BAAC,SAAG;4DACF,QAAQ;gEAAC;gEAAG;6DAAE;4DACd,SAAS;gEAAE,IAAI;gEAAS,IAAI;4DAAM;4DAClC,OAAM;;8EAGN,2BAAC,SAAG;oEAAC,IAAI;oEAAG,IAAI;oEAAG,IAAI;oEAAG,IAAI;oEAAG,IAAI;8EACnC,cAAA,2BAAC;wEACC,OAAO;4EACL,YAAY;4EACZ,QAAQ;4EACR,cAAc;4EACd,SAAS;4EACT,WAAW;4EACX,UAAU;wEACZ;kFAEA,cAAA,2BAAC,UAAI;4EAAC,QAAQ;4EAAC,OAAM;4EAAS,KAAK;;8FACjC,2BAAC,kBAAW;oFACV,OAAO;wFAAE,OAAO;wFAAW,UAAU;oFAAG;;;;;;8FAE1C,2BAAC;oFACC,MAAM;oFACN,OAAO;wFACL,UAAU;wFACV,OAAO;wFACP,YAAY;oFACd;8FAEC,EAAA,cAAA,KAAK,KAAK,cAAV,kCAAA,YAAY,QAAQ,KAAI;;;;;;8FAE3B,2BAAC;oFAAK,OAAO;wFAAE,UAAU;wFAAG,OAAO;oFAAO;8FAAG;;;;;;;;;;;;;;;;;;;;;;8EAQnD,2BAAC,SAAG;oEAAC,IAAI;oEAAG,IAAI;oEAAG,IAAI;oEAAG,IAAI;oEAAG,IAAI;8EACnC,cAAA,2BAAC;wEACC,OAAO;4EACL,YAAY;4EACZ,QAAQ;4EACR,cAAc;4EACd,SAAS;4EACT,WAAW;4EACX,UAAU;wEACZ;kFAEA,cAAA,2BAAC,UAAI;4EAAC,QAAQ;4EAAC,OAAM;4EAAS,KAAK;;8FACjC,2BAAC,mBAAY;oFACX,OAAO;wFAAE,OAAO;wFAAW,UAAU;oFAAG;;;;;;8FAE1C,2BAAC;oFACC,MAAM;oFACN,OAAO;wFACL,UAAU;wFACV,OAAO;wFACP,YAAY;oFACd;8FAEC,EAAA,eAAA,KAAK,KAAK,cAAV,mCAAA,aAAY,SAAS,KAAI;;;;;;8FAE5B,2BAAC;oFAAK,OAAO;wFAAE,UAAU;wFAAG,OAAO;oFAAO;8FAAG;;;;;;;;;;;;;;;;;;;;;;8EAQnD,2BAAC,SAAG;oEAAC,IAAI;oEAAG,IAAI;oEAAG,IAAI;oEAAG,IAAI;oEAAG,IAAI;8EACnC,cAAA,2BAAC;wEACC,OAAO;4EACL,YAAY;4EACZ,QAAQ;4EACR,cAAc;4EACd,SAAS;4EACT,WAAW;4EACX,UAAU;wEACZ;kFAEA,cAAA,2BAAC,UAAI;4EAAC,QAAQ;4EAAC,OAAM;4EAAS,KAAK;;8FACjC,2BAAC,gCAAyB;oFACxB,OAAO;wFAAE,OAAO;wFAAW,UAAU;oFAAG;;;;;;8FAE1C,2BAAC;oFACC,MAAM;oFACN,OAAO;wFACL,UAAU;wFACV,OAAO;wFACP,YAAY;oFACd;8FAEC,EAAA,eAAA,KAAK,KAAK,cAAV,mCAAA,aAAY,QAAQ,KAAI;;;;;;8FAE3B,2BAAC;oFAAK,OAAO;wFAAE,UAAU;wFAAG,OAAO;oFAAO;8FAAG;;;;;;;;;;;;;;;;;;;;;;8EAQnD,2BAAC,SAAG;oEAAC,IAAI;oEAAG,IAAI;oEAAG,IAAI;oEAAG,IAAI;oEAAG,IAAI;8EACnC,cAAA,2BAAC;wEACC,OAAO;4EACL,YAAY;4EACZ,QAAQ;4EACR,cAAc;4EACd,SAAS;4EACT,WAAW;4EACX,UAAU;wEACZ;kFAEA,cAAA,2BAAC,UAAI;4EAAC,QAAQ;4EAAC,OAAM;4EAAS,KAAK;;8FACjC,2BAAC,gCAAyB;oFACxB,OAAO;wFAAE,OAAO;wFAAW,UAAU;oFAAG;;;;;;8FAE1C,2BAAC;oFACC,MAAM;oFACN,OAAO;wFACL,UAAU;wFACV,OAAO;wFACP,YAAY;oFACd;8FAEC,EAAA,eAAA,KAAK,KAAK,cAAV,mCAAA,aAAY,OAAO,KAAI;;;;;;8FAE1B,2BAAC;oFAAK,OAAO;wFAAE,UAAU;wFAAG,OAAO;oFAAO;8FAAG;;;;;;;;;;;;;;;;;;;;;;8EAQnD,2BAAC,SAAG;oEAAC,IAAI;oEAAI,IAAI;oEAAG,IAAI;oEAAG,IAAI;oEAAG,IAAI;8EACpC,cAAA,2BAAC,UAAI;wEAAC,SAAQ;wEAAM,OAAM;wEAAS,KAAK;kFACrC,KAAK,SAAS,iBACb,2BAAC,aAAO;4EAAC,OAAM;sFACb,cAAA,2BAAC,YAAM;gFACL,MAAK;gFACL,MAAK;gFACL,oBAAM,2BAAC,sBAAe;oFAAC,OAAO;wFAAE,UAAU;oFAAG;;;;;;gFAC7C,SAAS,CAAC;oFACR,EAAE,eAAe;oFACjB,qBAAqB;gFACvB;gFACA,OAAO;oFACL,OAAO;oFACP,SAAS;oFACT,YAAY;oFACZ,gBAAgB;oFAChB,OAAO;oFACP,QAAQ;oFACR,SAAS;oFACT,cAAc;oFACd,QAAQ;oFACR,YAAY;oFACZ,YAAY;gFACd;gFACA,cAAc,CAAC;oFACb,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG;oFACnC,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,GAAG;gFACtC;gFACA,cAAc,CAAC;oFACb,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG;oFACnC,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,GAAG;gFACtC;;;;;;;;;;mGAIJ,2BAAC,aAAO;4EAAC,OAAM;sFACb,cAAA,2BAAC,YAAM;gFACL,MAAK;gFACL,MAAK;gFACL,oBAAM,2BAAC,qBAAc;oFAAC,OAAO;wFAAE,UAAU;oFAAG;;;;;;gFAC5C,SAAS,CAAC;oFACR,EAAE,eAAe;oFACjB,gBAAgB;gFAClB;gFACA,OAAO;oFACL,OAAO;oFACP,SAAS;oFACT,YAAY;oFACZ,gBAAgB;oFAChB,OAAO;oFACP,QAAQ;oFACR,SAAS;oFACT,cAAc;oFACd,QAAQ;oFACR,YAAY;oFACZ,YAAY;gFACd;gFACA,cAAc,CAAC;oFACb,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG;oFACnC,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,GAAG;gFACtC;gFACA,cAAc,CAAC;oFACb,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG;oFACnC,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,GAAG;gFACtC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAkBhC,2BAAC,4BAAmB;4BAClB,SAAS;4BACT,UAAU;gCACR,8BAA8B;gCAC9B,gBAAgB;4BAClB;4BACA,MAAM;4BACN,WAAW;;;;;;sCAIb,2BAAC,WAAK;4BACJ,OAAM;4BACN,MAAM;4BACN,UAAU;gCACR,yBAAyB;gCACzB,gBAAgB;4BAClB;4BACA,QAAQ;8CACN,2BAAC,YAAM;oCAEL,SAAS;wCACP,yBAAyB;wCACzB,gBAAgB;oCAClB;8CACD;mCALK;;;;;8CAQN,2BAAC,YAAM;oCAEL,MAAK;oCACL,MAAM;oCACN,SAAS;8CACV;mCAJK;;;;;6BAOP;sCAED,cAAA,2BAAC;gCAAI,OAAO;oCAAE,WAAW;oCAAU,SAAS;gCAAS;;kDACnD,2BAAC;;4CAAE;0DAAQ,2BAAC;0DAAQ,yBAAA,mCAAA,aAAc,IAAI;;;;;;4CAAU;;;;;;;kDAChD,2BAAC;wCAAE,OAAO;4CAAE,OAAO;wCAAU;kDAAG;;;;;;;;;;;;;;;;;;;YAO1C;eA19BM;;oBAoCsC,aAAQ;;;iBApC9C;gBA49BN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;IDzmCD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;SAAsC;QAAC,gCAA+B;YAAC;SAAqB;QAAC,kCAAiC;YAAC;YAAS;SAAwB;IAAA;;AACrjB"}